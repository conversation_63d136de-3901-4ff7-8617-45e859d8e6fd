<?php
// app/Views/report_nasp/report_nasp_index.php

/**
 * NASP Plans Report - Index View
 *
 * @var array $plans
 * @var array $apas
 * @var array $dips
 * @var array $specificAreas
 * @var array $objectives
 * @var array $outputs
 * @var array $indicators
 * @var array $chartData
 * @var string $title
 * @var string $dateFrom
 * @var string $dateTo
 */
?>
<?= $this->extend('templates/system_template') ?>

<?= $this->section('head') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css">

<!-- Custom styles -->
<style>
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 20px;
    }
    .copy-chart-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
    }
    .table th, .table td {
        white-space: nowrap;
        min-width: 120px;
    }
    .filter-card {
        background: linear-gradient(135deg, #6ba84f 0%, #5a9142 100%);
        color: white;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h4 class="card-title mb-2">NASP Plans Report</h4>
                    <p class="card-text mb-0">Comprehensive reporting dashboard for National Agriculture Sector Plan (NASP) data with activity linkages, filtering, and export capabilities.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card filter-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-filter me-2"></i>Date Range Filter
                    </h5>
                    <form method="GET" action="<?= base_url('reports/nasp') ?>" class="row g-3">
                        <div class="col-md-4">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?= $dateFrom ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?= $dateTo ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-light me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="<?= base_url('reports/nasp') ?>" class="btn btn-outline-light">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Visual Analytics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Status Distribution Chart -->
                        <div class="col-md-6 mb-4">
                            <div class="chart-container">
                                <button class="btn btn-sm btn-outline-secondary copy-chart-btn" onclick="copyChart('statusDistributionChart')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <canvas id="statusDistributionChart"></canvas>
                            </div>
                        </div>

                        <!-- Entities by Plan Chart -->
                        <div class="col-md-6 mb-4">
                            <div class="chart-container">
                                <button class="btn btn-sm btn-outline-secondary copy-chart-btn" onclick="copyChart('entitiesByPlanChart')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <canvas id="entitiesByPlanChart"></canvas>
                            </div>
                        </div>

                        <!-- APAs by Plan Chart -->
                        <div class="col-md-6 mb-4">
                            <div class="chart-container">
                                <button class="btn btn-sm btn-outline-secondary copy-chart-btn" onclick="copyChart('apasByPlanChart')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <canvas id="apasByPlanChart"></canvas>
                            </div>
                        </div>

                        <!-- DIPs by APA Chart -->
                        <div class="col-md-6 mb-4">
                            <div class="chart-container">
                                <button class="btn btn-sm btn-outline-secondary copy-chart-btn" onclick="copyChart('dipsByApaChart')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <canvas id="dipsByApaChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>Data Tables
                    </h5>
                </div>
                <div class="card-body">

                    <!-- NASP Plans Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">NASP Plans</h6>
                        <div class="table-container">
                            <table id="plansTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Date From</th>
                                        <th>Date To</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($plans as $plan): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td><?= esc($plan['code']) ?></td>
                                        <td><?= esc($plan['title']) ?></td>
                                        <td><?= esc($plan['date_from']) ?></td>
                                        <td><?= esc($plan['date_to']) ?></td>
                                        <td><?= esc($plan['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $plan['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- APAs Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Agriculture Priority Areas (APAs)</h6>
                        <div class="table-container">
                            <table id="apasTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>NASP Plan</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($apas as $apa): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($plans as $plan) {
                                                if ($plan['id'] == $apa['parent_id']) {
                                                    echo esc($plan['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($apa['code']) ?></td>
                                        <td><?= esc($apa['title']) ?></td>
                                        <td><?= esc($apa['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $apa['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- DIPs Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Deliberate Intervention Programs (DIPs)</h6>
                        <div class="table-container">
                            <table id="dipsTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>APA</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($dips as $dip): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($apas as $apa) {
                                                if ($apa['id'] == $dip['parent_id']) {
                                                    echo esc($apa['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($dip['code']) ?></td>
                                        <td><?= esc($dip['title']) ?></td>
                                        <td><?= esc($dip['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $dip['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Specific Areas Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Specific Areas</h6>
                        <div class="table-container">
                            <table id="specificAreasTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>DIP</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($specificAreas as $sa): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($dips as $dip) {
                                                if ($dip['id'] == $sa['parent_id']) {
                                                    echo esc($dip['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($sa['code']) ?></td>
                                        <td><?= esc($sa['title']) ?></td>
                                        <td><?= esc($sa['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $sa['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Objectives Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Objectives</h6>
                        <div class="table-container">
                            <table id="objectivesTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Specific Area</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($objectives as $objective): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($specificAreas as $sa) {
                                                if ($sa['id'] == $objective['parent_id']) {
                                                    echo esc($sa['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($objective['code']) ?></td>
                                        <td><?= esc($objective['title']) ?></td>
                                        <td><?= esc($objective['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $objective['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Outputs Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Outputs</h6>
                        <div class="table-container">
                            <table id="outputsTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Objective</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Activities</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($outputs as $output): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($objectives as $objective) {
                                                if ($objective['id'] == $output['parent_id']) {
                                                    echo esc($objective['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($output['code']) ?></td>
                                        <td><?= esc($output['title']) ?></td>
                                        <td><?= esc($output['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                        <td><?= $output['activity_count'] ?? 0 ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Indicators Table -->
                    <div class="mb-5">
                        <h6 class="mb-3">Indicators</h6>
                        <div class="table-container">
                            <table id="indicatorsTable" class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Output</th>
                                        <th>Code</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($indicators as $indicator): ?>
                                    <tr>
                                        <td><?= $counter++ ?></td>
                                        <td>
                                            <?php
                                            foreach ($outputs as $output) {
                                                if ($output['id'] == $indicator['parent_id']) {
                                                    echo esc($output['code']);
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?= esc($indicator['code']) ?></td>
                                        <td><?= esc($indicator['title']) ?></td>
                                        <td><?= esc($indicator['nasp_status']) == 1 ? 'Active' : 'Inactive' ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Chart.js - Load first -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

<!-- DataTables JavaScript -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

<!-- Toastr for notifications -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

<script>
// Wait for Chart.js to load before initializing
function initializeCharts() {
    if (typeof Chart === 'undefined') {
        setTimeout(initializeCharts, 100);
        return;
    }

$(document).ready(function() {
    // Chart color palette
    const colors = {
        chartColors: [
            '#6ba84f', '#1a237e', '#ff6b6b', '#4ecdc4', '#45b7d1',
            '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'
        ]
    };

    // DataTables configuration
    const dataTableConfig = {
        responsive: true,
        pageLength: -1,
        lengthChange: false,
        searching: true,
        ordering: true,
        info: true,
        autoWidth: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: ':visible'
                }
            }
        ]
    };

    // Initialize DataTables for all tables
    $('#plansTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP Plans Report'
        }]
    });

    $('#apasTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP APAs Report'
        }]
    });

    $('#dipsTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP DIPs Report'
        }]
    });

    $('#specificAreasTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP Specific Areas Report'
        }]
    });

    $('#objectivesTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP Objectives Report'
        }]
    });

    $('#outputsTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP Outputs Report'
        }]
    });

    $('#indicatorsTable').DataTable({
        ...dataTableConfig,
        buttons: [{
            ...dataTableConfig.buttons[0],
            title: 'NASP Indicators Report'
        }]
    });

    // Chart copy functionality
    window.copyChart = function(chartId) {
        const canvas = document.getElementById(chartId);
        if (canvas) {
            canvas.toBlob(function(blob) {
                const item = new ClipboardItem({ "image/png": blob });
                navigator.clipboard.write([item]).then(function() {
                    toastr.success('Chart copied to clipboard!');
                }).catch(function(err) {
                    console.error('Failed to copy chart: ', err);
                    toastr.error('Failed to copy chart to clipboard');
                });
            });
        }
    };

    // Chart data from PHP
    const chartData = <?= json_encode($chartData) ?>;

    // 1. Status Distribution Chart
    const statusDistributionCtx = document.getElementById('statusDistributionChart').getContext('2d');
    const statusData = chartData.statusCounts || {};

    const statusLabels = ['Plans', 'APAs', 'DIPs', 'Specific Areas', 'Objectives', 'Outputs', 'Indicators'];
    const activeData = [
        statusData.plans?.active || 0,
        statusData.apas?.active || 0,
        statusData.dips?.active || 0,
        statusData.specificAreas?.active || 0,
        statusData.objectives?.active || 0,
        statusData.outputs?.active || 0,
        statusData.indicators?.active || 0
    ];
    const inactiveData = [
        statusData.plans?.inactive || 0,
        statusData.apas?.inactive || 0,
        statusData.dips?.inactive || 0,
        statusData.specificAreas?.inactive || 0,
        statusData.objectives?.inactive || 0,
        statusData.outputs?.inactive || 0,
        statusData.indicators?.inactive || 0
    ];

    new Chart(statusDistributionCtx, {
        type: 'bar',
        data: {
            labels: statusLabels,
            datasets: [{
                label: 'Active',
                data: activeData,
                backgroundColor: colors.chartColors[0],
                borderWidth: 1
            }, {
                label: 'Inactive',
                data: inactiveData,
                backgroundColor: colors.chartColors[1],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Status Distribution'
                }
            },
            scales: {
                x: {
                    stacked: true,
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // 2. Entities by Plan Chart
    const entitiesByPlanCtx = document.getElementById('entitiesByPlanChart').getContext('2d');
    const entitiesByPlanData = chartData.entitiesByPlan || {};

    const planLabels = [];
    const planDatasets = [
        { label: 'APAs', data: [], backgroundColor: colors.chartColors[0] },
        { label: 'DIPs', data: [], backgroundColor: colors.chartColors[1] },
        { label: 'Specific Areas', data: [], backgroundColor: colors.chartColors[2] },
        { label: 'Objectives', data: [], backgroundColor: colors.chartColors[3] },
        { label: 'Outputs', data: [], backgroundColor: colors.chartColors[4] },
        { label: 'Indicators', data: [], backgroundColor: colors.chartColors[5] }
    ];

    Object.keys(entitiesByPlanData).forEach(key => {
        const plan = entitiesByPlanData[key];
        planLabels.push(plan.title);
        planDatasets[0].data.push(plan.apas || 0);
        planDatasets[1].data.push(plan.dips || 0);
        planDatasets[2].data.push(plan.specificAreas || 0);
        planDatasets[3].data.push(plan.objectives || 0);
        planDatasets[4].data.push(plan.outputs || 0);
        planDatasets[5].data.push(plan.indicators || 0);
    });

    new Chart(entitiesByPlanCtx, {
        type: 'bar',
        data: {
            labels: planLabels,
            datasets: planDatasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Entity Counts by NASP Plan'
                }
            },
            scales: {
                x: {
                    stacked: true,
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // 3. APAs by Plan Chart
    const apasByPlanCtx = document.getElementById('apasByPlanChart').getContext('2d');
    const apasByPlanData = chartData.apasByPlan || {};

    const apaPlanLabels = [];
    const apaPlanValues = [];

    Object.keys(apasByPlanData).forEach(key => {
        if (apasByPlanData[key].count > 0) {
            apaPlanLabels.push(apasByPlanData[key].title);
            apaPlanValues.push(apasByPlanData[key].count);
        }
    });

    new Chart(apasByPlanCtx, {
        type: 'pie',
        data: {
            labels: apaPlanLabels,
            datasets: [{
                data: apaPlanValues,
                backgroundColor: colors.chartColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'APAs Distribution by Plan'
                }
            }
        }
    });

    // 4. DIPs by APA Chart
    const dipsByApaCtx = document.getElementById('dipsByApaChart').getContext('2d');
    const dipsByApaData = chartData.dipsByApa || {};

    const apaLabels = [];
    const apaValues = [];

    Object.keys(dipsByApaData).forEach(key => {
        if (dipsByApaData[key].count > 0) {
            apaLabels.push(dipsByApaData[key].title);
            apaValues.push(dipsByApaData[key].count);
        }
    });

    new Chart(dipsByApaCtx, {
        type: 'doughnut',
        data: {
            labels: apaLabels,
            datasets: [{
                data: apaValues,
                backgroundColor: colors.chartColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'DIPs Distribution by APA'
                }
            }
        }
    });
});

}

// Initialize charts when page loads
initializeCharts();
</script>
<?= $this->endSection() ?>
